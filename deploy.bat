@echo off
chcp 65001 >nul
echo ========================================
echo Ki2API 部署脚本
echo ========================================

echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.9+: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo 检查并安装依赖包...
pip show fastapi >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装Python依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 依赖包安装失败
        echo 尝试使用国内镜像源...
        pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
        if %errorlevel% neq 0 (
            echo ❌ 依赖包安装失败，请检查网络连接
            pause
            exit /b 1
        )
    )
)

echo ✅ 依赖包检查通过

echo 选择部署方式:
echo 1. 本地直接运行 (推荐)
echo 2. 尝试Docker构建 (需要网络)
echo 3. 创建Windows服务
echo 4. 退出
set /p choice=请选择 (1-4): 

if "%choice%"=="1" (
    goto :local_run
) else if "%choice%"=="2" (
    goto :docker_build
) else if "%choice%"=="3" (
    goto :windows_service
) else (
    echo 退出部署脚本
    pause
    exit /b 0
)

:local_run
echo ========================================
echo 🚀 启动Ki2API本地服务
echo ========================================
echo 服务地址: http://localhost:8989
echo 健康检查: http://localhost:8989/health
echo API文档: http://localhost:8989/docs
echo 按 Ctrl+C 停止服务
echo ========================================
python app.py
goto :end

:docker_build
echo ========================================
echo 🐳 尝试Docker构建
echo ========================================
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装或未启动
    pause
    goto :end
)

echo 构建Docker镜像...
docker build -t ki2api .
if %errorlevel% neq 0 (
    echo ❌ Docker构建失败
    echo 建议配置Docker镜像源后重试
    pause
    goto :end
)

echo ✅ Docker镜像构建成功
echo 启动Docker容器...
docker run -d --name ki2api -p 8989:8989 -e API_KEY=ki2api-key-2024 ki2api
echo ✅ 容器已启动: http://localhost:8989
goto :end

:windows_service
echo ========================================
echo 🔧 创建Windows服务 (需要管理员权限)
echo ========================================
echo 此功能需要额外配置，建议使用本地运行方式
pause
goto :end

:end
pause
