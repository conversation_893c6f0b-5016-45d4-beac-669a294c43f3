#!/usr/bin/env python3
"""
调试Kiro API调用
"""

import os
import json
import httpx
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

KIRO_ACCESS_TOKEN = os.getenv("KIRO_ACCESS_TOKEN")
KIRO_REFRESH_TOKEN = os.getenv("KIRO_REFRESH_TOKEN")
KIRO_BASE_URL = "https://codewhisperer.us-east-1.amazonaws.com/generateAssistantResponse"
PROFILE_ARN = "arn:aws:codewhisperer:us-east-1:699475941385:profile/EHGA3GRVQMUK"

async def test_kiro_api():
    print("🔍 调试Kiro API调用...")
    print(f"Access Token: {KIRO_ACCESS_TOKEN[:50]}..." if KIRO_ACCESS_TOKEN else "❌ 无Access Token")
    print(f"Refresh Token: {KIRO_REFRESH_TOKEN[:50]}..." if KIRO_REFRESH_TOKEN else "❌ 无Refresh Token")
    
    if not KIRO_ACCESS_TOKEN:
        print("❌ 缺少访问令牌")
        return
    
    # 构建请求数据
    request_data = {
        "profileArn": PROFILE_ARN,
        "conversationState": {
            "chatTriggerType": "MANUAL",
            "conversationId": "test-conversation-123",
            "currentMessage": {
                "userInputMessage": {
                    "content": "你好，请简单介绍一下自己",
                    "modelId": "CLAUDE_SONNET_4_20250514_V1_0",
                    "origin": "AI_EDITOR",
                    "userInputMessageContext": {}
                }
            },
            "history": []
        }
    }
    
    headers = {
        "Authorization": f"Bearer {KIRO_ACCESS_TOKEN}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print(f"\n📤 请求URL: {KIRO_BASE_URL}")
    print(f"📤 请求头: {json.dumps({k: v[:50] + '...' if k == 'Authorization' else v for k, v in headers.items()}, indent=2)}")
    print(f"📤 请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    try:
        async with httpx.AsyncClient() as client:
            print("\n🚀 发送请求...")
            response = await client.post(
                KIRO_BASE_URL,
                headers=headers,
                json=request_data,
                timeout=30
            )
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📊 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                print("✅ 请求成功!")
                try:
                    response_data = response.json()
                    print(f"📝 响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"📝 响应内容 (原始): {response.text}")
            else:
                print("❌ 请求失败!")
                print(f"错误内容: {response.text}")
                
                # 如果是403，尝试刷新token
                if response.status_code == 403:
                    print("\n🔄 尝试刷新token...")
                    await refresh_token()
                    
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        import traceback
        traceback.print_exc()

async def refresh_token():
    if not KIRO_REFRESH_TOKEN:
        print("❌ 无刷新令牌")
        return None
    
    refresh_url = "https://prod.us-east-1.auth.desktop.kiro.dev/refreshToken"
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                refresh_url,
                json={"refreshToken": KIRO_REFRESH_TOKEN},
                timeout=30
            )
            
            print(f"刷新token响应状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                new_access_token = data.get("accessToken")
                print(f"✅ Token刷新成功: {new_access_token[:50]}...")
                
                # 更新.env文件
                with open('.env', 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换access token
                import re
                content = re.sub(
                    r'KIRO_ACCESS_TOKEN=.*',
                    f'KIRO_ACCESS_TOKEN={new_access_token}',
                    content
                )
                
                with open('.env', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ .env文件已更新")
                return new_access_token
            else:
                print(f"❌ Token刷新失败: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Token刷新异常: {e}")
        return None

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_kiro_api())
