# Docker网络问题解决方案

## 问题诊断

您遇到的错误表明Docker无法连接到Docker Hub：
```
failed to fetch oauth token: Post "https://auth.docker.io/token"
```

## 解决方案

### 1. 配置Docker镜像源（最有效）

**步骤：**
1. 打开Docker Desktop
2. 点击右上角设置图标（齿轮）
3. 进入 `Settings` → `Docker Engine`
4. 将配置修改为：

```json
{
  "builder": {
    "gc": {
      "defaultKeepStorage": "20GB",
      "enabled": true
    }
  },
  "experimental": false,
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://dockerproxy.com",
    "https://docker.nju.edu.cn"
  ]
}
```

5. 点击 `Apply & Restart`
6. 等待Docker重启完成

### 2. 检查网络连接

```bash
# 测试Docker Hub连接
curl -I https://registry-1.docker.io/

# 测试镜像源连接
curl -I https://docker.mirrors.ustc.edu.cn/
```

### 3. 清理Docker缓存

```bash
# 清理构建缓存
docker builder prune -a

# 清理所有未使用的资源
docker system prune -a
```

### 4. 重置Docker网络

```bash
# 重置Docker网络设置
docker network prune
```

### 5. 检查代理设置

如果您使用代理，需要在Docker Desktop中配置：
1. `Settings` → `Resources` → `Proxies`
2. 配置HTTP/HTTPS代理
3. 或者禁用代理

### 6. 使用离线镜像

如果网络问题无法解决，可以：
1. 下载离线镜像文件
2. 使用 `docker load` 导入
3. 或使用本地运行方式

## 推荐方案

**当前建议：使用本地运行**
```bash
# 运行部署脚本
deploy.bat

# 选择选项1：本地直接运行
```

这样可以避免Docker网络问题，直接在本地运行Ki2API服务。

## 验证服务

无论使用哪种方式，都可以通过以下方式验证：

```bash
# 健康检查
curl http://localhost:8989/health

# 查看API文档
# 浏览器访问: http://localhost:8989/docs
```
