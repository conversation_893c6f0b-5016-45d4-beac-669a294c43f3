# 使用Ubuntu基础镜像，避免Python镜像网络问题
FROM ubuntu:20.04

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

WORKDIR /app

# 安装Python和系统依赖
RUN apt-get update && apt-get install -y --fix-missing \
    python3 \
    python3-pip \
    python3-dev \
    gcc \
    curl \
    && ln -sf /usr/bin/python3 /usr/bin/python \
    && ln -sf /usr/bin/pip3 /usr/bin/pip \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app.py token_reader.py entrypoint.py entrypoint.sh ./

# Make entrypoint executable
RUN chmod +x entrypoint.sh

# Create .aws directory structure
RUN mkdir -p /root/.aws/sso/cache

# Expose port
EXPOSE 8989

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8989/health || exit 1

# Use Python entrypoint script for smart startup (避免Windows换行符问题)
ENTRYPOINT ["python", "entrypoint.py"]