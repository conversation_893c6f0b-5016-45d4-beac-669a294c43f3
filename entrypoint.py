#!/usr/bin/env python3
"""
智能启动脚本 - Python版本
自动读取token并启动服务
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🚀 Ki2API 启动中...")
    
    # 检查是否存在token文件
    token_file = Path.home() / ".aws" / "sso" / "cache" / "kiro-auth-token.json"
    
    if token_file.exists():
        print("📁 发现token文件，正在读取...")
        
        # 运行token读取脚本
        try:
            result = subprocess.run([sys.executable, "token_reader.py"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Token配置完成")
            else:
                print("⚠️  Token读取失败，继续启动（需要手动配置token）")
                print(f"错误信息: {result.stderr}")
        except Exception as e:
            print(f"⚠️  Token读取脚本执行失败: {e}")
    else:
        print(f"⚠️  未找到token文件: {token_file}")
        print("请确保已登录Kiro，或手动设置环境变量")
    
    # 检查环境变量
    if not os.getenv("KIRO_ACCESS_TOKEN") or not os.getenv("KIRO_REFRESH_TOKEN"):
        print("⚠️  环境变量未设置，尝试从.env文件加载...")
        env_file = Path(".env")
        if env_file.exists():
            # 读取.env文件并设置环境变量
            try:
                with open(env_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()
                print("✅ 已从.env文件加载配置")
            except Exception as e:
                print(f"❌ 读取.env文件失败: {e}")
        else:
            print("❌ 未找到token配置，服务可能无法正常工作")
            print("请设置 KIRO_ACCESS_TOKEN 和 KIRO_REFRESH_TOKEN 环境变量")
    
    # 启动应用
    print("🎯 启动FastAPI服务...")
    try:
        # 直接导入并运行app
        import app
        import uvicorn
        uvicorn.run(app.app, host="0.0.0.0", port=8989)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
