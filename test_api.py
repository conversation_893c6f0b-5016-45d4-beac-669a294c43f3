#!/usr/bin/env python3
"""
测试Ki2API的聊天功能
"""

import requests
import json

def test_chat():
    url = "http://localhost:8989/v1/chat/completions"
    headers = {
        "Authorization": "Bearer ki2api-key-2024",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "claude-sonnet-4-20250514",
        "messages": [
            {"role": "user", "content": "你好，请简单介绍一下自己"}
        ],
        "max_tokens": 500
    }
    
    print("🚀 发送聊天请求...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"\n📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功!")
            print(f"📝 回复内容: {result['choices'][0]['message']['content']}")
        else:
            print("❌ 请求失败!")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def test_health():
    url = "http://localhost:8989/health"
    try:
        response = requests.get(url, timeout=5)
        print(f"健康检查: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_models():
    url = "http://localhost:8989/v1/models"
    headers = {"Authorization": "Bearer ki2api-key-2024"}
    try:
        response = requests.get(url, headers=headers, timeout=5)
        print(f"模型列表: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"模型列表获取失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始测试Ki2API...")
    
    # 测试健康检查
    if test_health():
        print("✅ 健康检查通过")
    else:
        print("❌ 健康检查失败")
        exit(1)
    
    # 测试模型列表
    if test_models():
        print("✅ 模型列表获取成功")
    else:
        print("❌ 模型列表获取失败")
        exit(1)
    
    # 测试聊天功能
    test_chat()
