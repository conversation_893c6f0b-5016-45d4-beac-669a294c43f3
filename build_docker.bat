@echo off
echo ========================================
echo Ki2API Docker 构建脚本
echo ========================================

echo 检查Docker状态...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未启动
    echo 请确保Docker Desktop正在运行
    pause
    exit /b 1
)

echo 构建Docker镜像...
docker build -t ki2api .
if %errorlevel% neq 0 (
    echo 错误: Docker镜像构建失败
    echo 请检查网络连接和Docker配置
    pause
    exit /b 1
)

echo ========================================
echo 镜像构建成功！
echo ========================================

echo 选择运行方式:
echo 1. 使用docker run命令运行
echo 2. 使用docker-compose运行
echo 3. 退出
set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 启动容器...
    docker run -d --name ki2api -p 8989:8989 -e API_KEY=ki2api-key-2024 ki2api
    echo 容器已启动，访问 http://localhost:8989
) else if "%choice%"=="2" (
    echo 使用docker-compose启动...
    docker-compose up -d
    echo 服务已启动，访问 http://localhost:8989
) else (
    echo 退出构建脚本
)

pause
