@echo off
echo ========================================
echo Ki2API 本地运行脚本
echo ========================================

echo 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.11+
    pause
    exit /b 1
)

echo 检查依赖包...
pip show fastapi >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装Python依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 启动Ki2API服务...
echo 服务将在 http://localhost:8989 启动
echo 按 Ctrl+C 停止服务
echo ========================================

python app.py
